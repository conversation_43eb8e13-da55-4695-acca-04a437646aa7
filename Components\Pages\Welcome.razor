@page "/welcome"

<div class="w-[375px] h-[812px] bg-white mx-auto relative font-system overflow-hidden">
    <!-- Main Content -->
    <div class="pt-20 px-8 pb-8 flex flex-col items-center h-full">
        <!-- Logo Section -->
        <div class="flex flex-col items-center mb-4">
            <div class="mb-3">
                <svg width="60" height="50" viewBox="0 0 60 50" fill="none">
                    <!-- Gate structure -->
                    <rect x="8" y="15" width="4" height="25" fill="#4ECDC4" rx="1"/>
                    <rect x="15" y="12" width="4" height="28" fill="#4ECDC4" rx="1"/>
                    <rect x="22" y="10" width="4" height="30" fill="#4ECDC4" rx="1"/>
                    <rect x="29" y="8" width="4" height="32" fill="#4ECDC4" rx="1"/>
                    <rect x="36" y="10" width="4" height="30" fill="#4ECDC4" rx="1"/>
                    <rect x="43" y="12" width="4" height="28" fill="#4ECDC4" rx="1"/>
                    <rect x="50" y="15" width="4" height="25" fill="#4ECDC4" rx="1"/>
                    <!-- Base -->
                    <rect x="5" y="40" width="50" height="3" fill="#FF6B6B" rx="1"/>
                    <!-- Sale tag -->
                    <path d="M45 5 L55 5 L55 15 L50 20 L45 15 Z" fill="#FF6B6B"/>
                    <circle cx="50" cy="10" r="2" fill="white"/>
                </svg>
            </div>
            <h1 class="text-3xl font-bold my-0 tracking-wide">
                <span class="text-[#4ECDC4]">GATE</span><span class="text-[#FF6B6B]">SALE</span>
            </h1>
        </div>

        <!-- Tagline -->
        <p class="text-sm text-[#666666] text-center my-0 mb-12 leading-relaxed font-normal">A safe marketplace just for students.</p>

        <!-- Illustration -->
        <div class="relative w-[300px] h-[300px] mb-12">
            <!-- Background Card -->
            <div class="absolute w-full h-full bg-gradient-to-br from-[#E8F4F8] to-[#F0F8FF] rounded-3xl top-0 left-0 shadow-[0_8px_32px_rgba(0,0,0,0.08)]"></div>

            <!-- Students Container -->
            <div class="relative w-full h-full flex justify-center items-center z-10">
                <!-- Left Student (Boy) -->
                <div class="absolute w-20 h-40 left-10 top-15">
                    <!-- Head -->
                    <div class="relative w-12 h-12 mx-auto mb-1">
                        <!-- Hair -->
                        <div class="absolute w-12 h-10 bg-[#8B4513] rounded-t-3xl top-0"></div>
                        <!-- Face -->
                        <div class="absolute w-[42px] h-[42px] bg-[#FDBCB4] rounded-full top-4 left-1"></div>
                        <!-- Eyes -->
                        <div class="absolute top-[22px] left-[10px] w-8 h-2">
                            <div class="absolute w-[3px] h-[3px] bg-black rounded-full left-2"></div>
                            <div class="absolute w-[3px] h-[3px] bg-black rounded-full right-2"></div>
                        </div>
                        <!-- Mouth -->
                        <div class="absolute top-8 left-5 w-[6px] h-[3px] bg-[#FF6B6B] rounded-b-md"></div>
                    </div>

                    <!-- Body -->
                    <div class="relative w-14 h-[70px] mx-auto">
                        <!-- Jacket -->
                        <div class="w-14 h-12 bg-[#20B2AA] rounded-t-xl relative z-20"></div>
                        <!-- Inner Shirt -->
                        <div class="absolute w-9 h-6 top-5 left-[10px] bg-[#FF6B6B] rounded-t-lg z-10"></div>
                        <!-- Arms -->
                        <div class="absolute top-4 w-full h-9">
                            <!-- Left Arm -->
                            <div class="absolute w-[18px] h-10 bg-[#FDBCB4] rounded-lg left-[-10px] transform -rotate-12"></div>
                            <!-- Right Arm (extending) -->
                            <div class="absolute w-[18px] h-10 bg-[#FDBCB4] rounded-lg right-[-25px] transform -rotate-[30deg]"></div>
                        </div>
                    </div>

                    <!-- Legs -->
                    <div class="relative w-12 h-11 mx-auto">
                        <div class="w-12 h-10 bg-[#D2B48C] rounded-b-xl"></div>
                    </div>

                    <!-- Backpack -->
                    <div class="absolute w-7 h-9 bg-[#DC143C] rounded-lg top-12 right-[-12px]"></div>
                </div>

                <!-- Right Student (Girl) -->
                <div class="absolute w-20 h-40 right-10 top-15">
                    <!-- Head -->
                    <div class="relative w-12 h-12 mx-auto mb-1">
                        <!-- Hair -->
                        <div class="absolute w-12 h-10 bg-[#2C1810] rounded-t-3xl top-0"></div>
                        <!-- Face -->
                        <div class="absolute w-[42px] h-[42px] bg-[#8D5524] rounded-full top-4 left-1"></div>
                        <!-- Eyes -->
                        <div class="absolute top-[22px] left-[10px] w-8 h-2">
                            <div class="absolute w-[3px] h-[3px] bg-black rounded-full left-2"></div>
                            <div class="absolute w-[3px] h-[3px] bg-black rounded-full right-2"></div>
                        </div>
                        <!-- Mouth -->
                        <div class="absolute top-8 left-5 w-[6px] h-[3px] bg-[#FF6B6B] rounded-b-md"></div>
                    </div>

                    <!-- Body -->
                    <div class="relative w-14 h-[70px] mx-auto">
                        <!-- Jacket -->
                        <div class="w-14 h-12 bg-[#FF8C00] rounded-t-xl relative z-20"></div>
                        <!-- Inner Shirt -->
                        <div class="absolute w-9 h-6 top-5 left-[10px] bg-[#FFD700] rounded-t-lg z-10"></div>
                        <!-- Arms -->
                        <div class="absolute top-4 w-full h-9">
                            <!-- Left Arm (extending) -->
                            <div class="absolute w-[18px] h-10 bg-[#8D5524] rounded-lg left-[-25px] transform rotate-[30deg]"></div>
                            <!-- Right Arm -->
                            <div class="absolute w-[18px] h-10 bg-[#8D5524] rounded-lg right-[-10px] transform rotate-12"></div>
                        </div>
                    </div>

                    <!-- Legs -->
                    <div class="relative w-12 h-11 mx-auto">
                        <div class="w-12 h-10 bg-[#4169E1] rounded-b-xl"></div>
                    </div>
                </div>
            </div>

            <!-- Floating Icons -->
            <div class="absolute top-5 right-5 z-40 w-10 h-10 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-[0_4px_12px_rgba(0,0,0,0.15)]">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="#4CAF50"/>
                </svg>
            </div>
            <div class="absolute bottom-5 left-5 z-40 w-10 h-10 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-[0_4px_12px_rgba(0,0,0,0.15)]">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.1 16,12.7V16.2C16,16.8 15.4,17.3 14.8,17.3H9.2C8.6,17.3 8,16.8 8,16.2V12.7C8,12.1 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z" fill="#2196F3"/>
                </svg>
            </div>
        </div>

        <!-- Next Button -->
        <button class="w-[300px] h-13 bg-[#00BFFF] border-0 rounded-[26px] text-white text-lg font-semibold cursor-pointer mb-4 transition-all shadow-[0_4px_16px_rgba(0,191,255,0.3)] hover:bg-[#0099CC]">Next</button>

        <!-- Skip Text -->
        <p class="text-base text-[#999999] my-0 mb-10 cursor-pointer font-normal">Skip for now</p>

        <!-- Page Indicators -->
        <div class="flex gap-2 mt-auto pb-5">
            <span class="w-6 h-2 rounded bg-[#00BFFF] transition-all"></span>
            <span class="w-2 h-2 rounded bg-[#E0E0E0] transition-all"></span>
            <span class="w-2 h-2 rounded bg-[#E0E0E0] transition-all"></span>
        </div>
    </div>
</div>








