{"Version": 1, "Hash": "s3Zfpzm28HJuJchMzDaMlTJUUppqPW3zvlzVxRjtcVc=", "Source": "GateSale", "BasePath": "/", "Mode": "Root", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "GateSale\\wwwroot", "Source": "GateSale", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\0qse1rb7ey-jly3244nyj.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint=jly3244nyj}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zggpgoxjva", "Integrity": "o19yYaXACjMGgT4mazD0J2xHT8NjcaVhCNZ17mSo+Is=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint=6gzpyzhau4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "joqzyw7ssu", "Integrity": "LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint=4jtf2pvl4b}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lwxvo5jupf", "Integrity": "ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=e5tk7yf482}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9sb0w9jnhl", "Integrity": "qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=55aay04al6}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sciskma03l", "Integrity": "7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\index.html"}, {"Identity": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fbl5ivwo6k", "Integrity": "Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e5tk7yf482", "Integrity": "bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6gzpyzhau4", "Integrity": "SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/input#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4jtf2pvl4b", "Integrity": "QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\input.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jly3244nyj", "Integrity": "GVyNQV2F14/gUb9JT8ap2tsE+ljlbog/4hK0tT7x87I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\tailwind.css"}, {"Identity": "D:\\Projects\\GateSale\\wwwroot\\index.html", "SourceId": "GateSale", "SourceType": "Discovered", "ContentRoot": "D:\\Projects\\GateSale\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "55aay04al6", "Integrity": "2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}], "Endpoints": [{"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000571102227"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000571102227"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3376"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-bhCZyC/yM2+DaFXr8z25+5MeWfXWqMJNNA4ZLAXbjjU="}]}, {"Route": "css/app.e5tk7yf482.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\okv3f23xy4-e5tk7yf482.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1750"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5tk7yf482"}, {"Name": "label", "Value": "css/app.css.gz"}, {"Name": "integrity", "Value": "sha256-qQ+vo7zHfjEIsR7Ks2Vsx0CJAT5ngXzV3IUcVLWwEfw="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.6gzpyzhau4.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}]}, {"Route": "css/bootstrap/bootstrap.min.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\c4yfl1a1cx-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\x0awlxkggx-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:52:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css"}, {"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.4jtf2pvl4b.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4jtf2pvl4b"}, {"Name": "label", "Value": "css/input.css.gz"}, {"Name": "integrity", "Value": "sha256-ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\input.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLmmrMNbSHNvUiF8ciGFLAl7FsktmGtrndANCLvWEMw="}]}, {"Route": "css/input.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\l2n983ggca-4jtf2pvl4b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZbkhmclwGuRcL9jdCJSfLeY9Ploof1QSXrf3GOmBdrU="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\0qse1rb7ey-jly3244nyj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000306748466"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3259"}, {"Name": "ETag", "Value": "\"o19yYaXACjMGgT4mazD0J2xHT8NjcaVhCNZ17mSo+Is=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GVyNQV2F14/gUb9JT8ap2tsE+ljlbog/4hK0tT7x87I=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GVyNQV2F14/gUb9JT8ap2tsE+ljlbog/4hK0tT7x87I="}]}, {"Route": "css/tailwind.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11508"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GVyNQV2F14/gUb9JT8ap2tsE+ljlbog/4hK0tT7x87I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GVyNQV2F14/gUb9JT8ap2tsE+ljlbog/4hK0tT7x87I="}]}, {"Route": "css/tailwind.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\0qse1rb7ey-jly3244nyj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"o19yYaXACjMGgT4mazD0J2xHT8NjcaVhCNZ17mSo+Is=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o19yYaXACjMGgT4mazD0J2xHT8NjcaVhCNZ17mSo+Is="}]}, {"Route": "css/tailwind.jly3244nyj.css", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\0qse1rb7ey-jly3244nyj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000306748466"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3259"}, {"Name": "ETag", "Value": "\"o19yYaXACjMGgT4mazD0J2xHT8NjcaVhCNZ17mSo+Is=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GVyNQV2F14/gUb9JT8ap2tsE+ljlbog/4hK0tT7x87I=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jly3244nyj"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-GVyNQV2F14/gUb9JT8ap2tsE+ljlbog/4hK0tT7x87I="}]}, {"Route": "css/tailwind.jly3244nyj.css", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\css\\tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11508"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GVyNQV2F14/gUb9JT8ap2tsE+ljlbog/4hK0tT7x87I=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jly3244nyj"}, {"Name": "label", "Value": "css/tailwind.css"}, {"Name": "integrity", "Value": "sha256-GVyNQV2F14/gUb9JT8ap2tsE+ljlbog/4hK0tT7x87I="}]}, {"Route": "css/tailwind.jly3244nyj.css.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\0qse1rb7ey-jly3244nyj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"o19yYaXACjMGgT4mazD0J2xHT8NjcaVhCNZ17mSo+Is=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:38:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jly3244nyj"}, {"Name": "label", "Value": "css/tailwind.css.gz"}, {"Name": "integrity", "Value": "sha256-o19yYaXACjMGgT4mazD0J2xHT8NjcaVhCNZ17mSo+Is="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002314814815"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.55aay04al6.html.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55aay04al6"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002314814815"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html", "AssetFile": "D:\\Projects\\GateSale\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2XNGBGpWdi89nx2gl3keNGLhGJIgwUl2Rs8tbdYbQyM="}]}, {"Route": "index.html.gz", "AssetFile": "D:\\Projects\\GateSale\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\compressed\\uv3ua5xd0n-55aay04al6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "431"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 18:43:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7JecTSdtBZJCjOhtFuGnku7tIgECalbi3z/iuLMVsq0="}]}]}